# LLM设置功能说明

## 功能概述

本功能允许用户通过Web界面动态配置LLM（大语言模型）的参数，包括API Key、Base URL和模型名称，无需重启应用程序。

## 主要特性

1. **Web界面配置**: 在dashboard.html页面中新增"LLM设置"标签页
2. **实时配置**: 修改设置后立即生效，无需重启应用
3. **配置持久化**: 设置保存到`llm_config.json`文件中
4. **环境变量兼容**: 支持从环境变量`DASHSCOPE_API_KEY`读取API Key
5. **多模型支持**: 支持多种预定义模型和自定义模型

## 使用方法

### 1. 通过Web界面设置

1. 打开 `http://localhost:端口/dashboard.html`
2. 点击"LLM设置"标签页
3. 填写以下信息：
   - **API Key**: 您的LLM服务API密钥
   - **Base URL**: LLM服务的基础URL地址
   - **模型名称**: 选择预定义模型或输入自定义模型名称
4. 点击"保存设置"按钮
5. 系统会显示"LLM设置已保存成功！"的提示

### 2. 支持的模型

预定义模型包括：
- `qwen-plus` (默认)
- `qwen-turbo`
- `qwen-max`
- `gpt-3.5-turbo`
- `gpt-4`
- `gpt-4-turbo`
- 自定义模型

### 3. 配置文件

配置保存在项目根目录的`llm_config.json`文件中，格式如下：

```json
{
  "api_key": "your_api_key_here",
  "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "model": "qwen-plus"
}
```

## API接口

### 获取当前设置
```
GET /get_llm_settings
```

响应：
```json
{
  "code": 0,
  "api_key": "sk-****",
  "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "model": "qwen-plus"
}
```

### 保存设置
```
POST /set_llm_settings
Content-Type: application/json

{
  "api_key": "your_api_key",
  "base_url": "https://your-llm-service.com/v1",
  "model": "your-model"
}
```

响应：
```json
{
  "code": 0,
  "msg": "设置保存成功"
}
```

## 文件说明

- `llm_config.py`: LLM配置管理模块
- `llm.py`: 修改后的LLM响应函数，支持动态配置
- `app.py`: 添加了LLM设置的API端点
- `web/dashboard.html`: 添加了LLM设置界面
- `test_llm_config.py`: 配置功能测试脚本

## 注意事项

1. **API Key安全**: API Key在界面上会被部分隐藏显示（只显示后4位）
2. **配置验证**: 保存时会验证必需字段不能为空
3. **错误处理**: 包含完整的错误处理和日志记录
4. **向后兼容**: 如果没有配置文件，会自动使用默认配置和环境变量

## 测试

运行测试脚本验证功能：
```bash
python test_llm_config.py
```

## 故障排除

1. **API Key未设置**: 检查配置文件或环境变量`DASHSCOPE_API_KEY`
2. **配置文件损坏**: 删除`llm_config.json`文件，系统会自动创建默认配置
3. **网络连接问题**: 检查Base URL是否正确，网络是否可达
