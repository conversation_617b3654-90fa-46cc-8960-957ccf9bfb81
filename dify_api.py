import requests
import time
import json
from basereal import BaseReal
from logger import logger


def question_dify(cont, nerfreal: BaseReal = None):
    """
    使用Dify API进行流式对话

    Args:
        cont: 用户输入的消息
        nerfreal: BaseReal实例，用于流式输出

    Returns:
        str: 完整的响应文本
    """
    api_key = 'app-4epx3XEP4QUudZ2IF83HGtGV'
    url = 'http://192.168.0.118:11180/v1/chat-messages'
    user = 'sgk324'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
    }

    data = {
        "inputs": {},
        "query": cont,
        "response_mode": "streaming",
        "conversation_id": "",
        "user": user,
    }

    starttime = time.time()

    try:
        # 发送流式请求
        response = requests.post(url, headers=headers, json=data, stream=True)
        response.raise_for_status()

        full_response = ""
        result = ""
        first = True

        # 处理流式响应
        for line in response.iter_lines(decode_unicode=True):
            if line:
                # Dify的流式响应格式: "data: {json_data}"
                if line.startswith('data: '):
                    try:
                        json_str = line[6:]  # 去掉 "data: " 前缀
                        if json_str.strip() == '[DONE]':  # 流结束标志
                            break

                        chunk_data = json.loads(json_str)

                        if first:
                            end = time.time()
                            logger.info(f"Dify Time to first chunk: {end-starttime}s")
                            first = False

                        # 检查是否是消息事件
                        if chunk_data.get('event') == 'message':
                            msg = chunk_data.get('answer', '')
                            if msg:
                                full_response += msg

                                # 如果有nerfreal实例，进行流式输出
                                if nerfreal:
                                    lastpos = 0
                                    # 按标点符号分割消息
                                    for i, char in enumerate(msg):
                                        if char in ",.!;:，。！？：；":
                                            result = result + msg[lastpos:i+1]
                                            lastpos = i + 1
                                            if len(result) > 10:
                                                logger.info(result)
                                                nerfreal.put_msg_txt(result)
                                                result = ""
                                    result = result + msg[lastpos:]

                        # 检查是否是消息结束事件
                        elif chunk_data.get('event') == 'message_end':
                            logger.info("Dify stream ended")
                            break

                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse JSON: {e}, line: {line}")
                        continue

        end = time.time()
        logger.info(f"Dify Time to last chunk: {end-starttime}s")

        # 发送剩余的文本
        if result and nerfreal:
            nerfreal.put_msg_txt(result)

        return full_response.strip() if full_response else "抱歉，没有收到有效响应。"

    except requests.exceptions.RequestException as e:
        logger.error(f"Dify API请求失败: {e}")
        error_msg = "抱歉，我现在太忙了，休息一会，请稍后再试。"

        if nerfreal:
            nerfreal.put_msg_txt(error_msg)

        return error_msg
    except Exception as e:
        logger.error(f"Dify API处理失败: {e}")
        error_msg = "抱歉，处理您的请求时出现了问题。"

        if nerfreal:
            nerfreal.put_msg_txt(error_msg)

        return error_msg

    finally:
        print("接口调用耗时 :" + str(time.time() - starttime))

def test_dify_api():
    """
    测试Dify API是否正常工作
    """
    print("测试Dify API...")

    try:
        query = "有什么套餐"
        print(f"发送查询: {query}")

        # 测试不带流式输出的调用
        response = question_dify(query)
        print(f"✓ Dify API连接测试成功")
        print(f"  响应: {response}")
        return True

    except Exception as e:
        print(f"✗ Dify API连接测试失败: {e}")
        return False


if __name__ == "__main__":
    test_dify_api()
