import time
import os
from basereal import BaseReal
from logger import logger
from llm_config import get_llm_config

def llm_response(message,nerfreal:BaseReal):
    start = time.perf_counter()
    from openai import OpenAI

    # 获取LLM配置
    config = get_llm_config()

    # 如果配置中没有API key，尝试从环境变量获取
    api_key = config.get("api_key") or os.getenv("DASHSCOPE_API_KEY")
    base_url = config.get("base_url", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    model = config.get("model", "qwen-plus")

    if not api_key:
        logger.error("未设置API Key，请在LLM设置中配置或设置DASHSCOPE_API_KEY环境变量")
        nerfreal.put_msg_txt("抱歉，LLM服务未正确配置，请检查API Key设置。")
        return

    client = OpenAI(
        api_key=api_key,
        base_url=base_url,
    )
    end = time.perf_counter()
    logger.info(f"llm Time init: {end-start}s")
    logger.info(f"使用模型: {model}, Base URL: {base_url}")

    completion = client.chat.completions.create(
        model=model,
        messages=[{'role': 'system', 'content': 'You are a helpful assistant.'},
                  {'role': 'user', 'content': message}],
        stream=True,
        # 通过以下设置，在流式输出的最后一行展示token使用信息
        stream_options={"include_usage": True}
    )
    result=""
    first = True
    for chunk in completion:
        if len(chunk.choices)>0:
            #print(chunk.choices[0].delta.content)
            if first:
                end = time.perf_counter()
                logger.info(f"llm Time to first chunk: {end-start}s")
                first = False
            msg = chunk.choices[0].delta.content

            # 检查msg是否为None，如果是则跳过这个chunk
            if msg is None:
                continue

            lastpos=0
            #msglist = re.split('[,.!;:，。！?]',msg)
            for i, char in enumerate(msg):
                if char in ",.!;:，。！？：；" :
                    result = result+msg[lastpos:i+1]
                    lastpos = i+1
                    if len(result)>10:
                        logger.info(result)
                        nerfreal.put_msg_txt(result)
                        result=""
            result = result+msg[lastpos:]
    end = time.perf_counter()
    logger.info(f"llm Time to last chunk: {end-start}s")
    nerfreal.put_msg_txt(result)    