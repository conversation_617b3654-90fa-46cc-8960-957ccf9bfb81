import json
import os
from logger import logger

# 默认LLM配置
DEFAULT_LLM_CONFIG = {
    "api_key": "",
    "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "model": "qwen-plus"
}

CONFIG_FILE = "llm_config.json"

def load_llm_config():
    """加载LLM配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有必需的键都存在
                merged_config = DEFAULT_LLM_CONFIG.copy()
                merged_config.update(config)
                return merged_config
        else:
            # 如果配置文件不存在，尝试从环境变量获取API key
            config = DEFAULT_LLM_CONFIG.copy()
            env_api_key = os.getenv("DASHSCOPE_API_KEY")
            if env_api_key:
                config["api_key"] = env_api_key
            return config
    except Exception as e:
        logger.error(f"加载LLM配置失败: {e}")
        # 返回默认配置，但尝试从环境变量获取API key
        config = DEFAULT_LLM_CONFIG.copy()
        env_api_key = os.getenv("DASHSCOPE_API_KEY")
        if env_api_key:
            config["api_key"] = env_api_key
        return config

def save_llm_config(config):
    """保存LLM配置"""
    try:
        # 验证配置
        if not isinstance(config, dict):
            raise ValueError("配置必须是字典类型")
        
        required_keys = ["api_key", "base_url", "model"]
        for key in required_keys:
            if key not in config:
                raise ValueError(f"缺少必需的配置项: {key}")
        
        # 保存到文件
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        logger.info("LLM配置已保存")
        return True
    except Exception as e:
        logger.error(f"保存LLM配置失败: {e}")
        return False

def get_llm_config():
    """获取当前LLM配置"""
    return load_llm_config()

def update_llm_config(api_key=None, base_url=None, model=None):
    """更新LLM配置"""
    config = load_llm_config()
    
    if api_key is not None:
        config["api_key"] = api_key
    if base_url is not None:
        config["base_url"] = base_url
    if model is not None:
        config["model"] = model
    
    return save_llm_config(config)
